<template>
  <div class="exchange-data">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">兑换记录</h1>
      <p class="text-gray-600">查看和管理积分商品兑换记录</p>
    </div>

    <!-- 兑换统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-shopping-bag" class="w-6 h-6 text-blue-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ exchangeStats.total }}</p>
          <p class="text-sm text-gray-500">总兑换次数</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-check-circle" class="w-6 h-6 text-green-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ exchangeStats.completed }}</p>
          <p class="text-sm text-gray-500">已完成</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-clock" class="w-6 h-6 text-yellow-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ exchangeStats.pending }}</p>
          <p class="text-sm text-gray-500">待处理</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-currency-dollar" class="w-6 h-6 text-purple-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ exchangeStats.totalPoints.toLocaleString() }}</p>
          <p class="text-sm text-gray-500">消费积分</p>
        </div>
      </UCard>
    </div>

    <!-- 筛选和搜索 -->
    <UCard class="mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索员工姓名、商品名称或订单号..."
            icon="i-heroicons-magnifying-glass"
          />
        </div>
        <div class="flex gap-2">
          <USelect
            v-model="selectedStatus"
            :options="statusOptions"
            placeholder="状态"
          />
          <USelect
            v-model="selectedProduct"
            :options="productOptions"
            placeholder="商品"
          />
          <UInput
            v-model="dateRange.start"
            type="date"
            placeholder="开始日期"
          />
          <UInput
            v-model="dateRange.end"
            type="date"
            placeholder="结束日期"
          />
          <UButton @click="resetFilters" variant="outline">重置</UButton>
          <UButton @click="exportExchanges" variant="outline">
            <UIcon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-2" />
            导出
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- 兑换记录表格 -->
    <UCard>
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">兑换记录</h3>
          <div class="flex space-x-2">
            <UButton @click="batchProcess" variant="outline" size="sm">
              批量处理
            </UButton>
          </div>
        </div>
      </template>

      <UTable
        :rows="filteredExchanges"
        :columns="columns"
        :loading="loading"
        v-model="selectedRows"
      >
        <template #orderNumber-data="{ row }">
          <div>
            <p class="text-sm font-medium text-gray-900">{{ row.orderNumber }}</p>
            <p class="text-xs text-gray-500">{{ formatDate(row.createdAt) }}</p>
          </div>
        </template>

        <template #employee-data="{ row }">
          <div class="flex items-center space-x-3">
            <UAvatar :alt="row.employee.name" size="sm" />
            <div>
              <p class="text-sm font-medium text-gray-900">{{ row.employee.name }}</p>
              <p class="text-xs text-gray-500">{{ row.employee.department }}</p>
            </div>
          </div>
        </template>

        <template #product-data="{ row }">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-gift" class="w-5 h-5 text-gray-500" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ row.product.name }}</p>
              <p class="text-xs text-gray-500">数量: {{ row.quantity }}</p>
            </div>
          </div>
        </template>

        <template #points-data="{ row }">
          <div class="text-center">
            <p class="text-sm font-semibold text-gray-900">{{ row.totalPoints }}</p>
            <p class="text-xs text-gray-500">积分</p>
          </div>
        </template>

        <template #status-data="{ row }">
          <UBadge
            :color="getStatusColor(row.status)"
            variant="soft"
          >
            {{ getStatusLabel(row.status) }}
          </UBadge>
        </template>

        <template #deliveryInfo-data="{ row }">
          <div>
            <p class="text-sm text-gray-900">{{ row.deliveryMethod }}</p>
            <p class="text-xs text-gray-500" v-if="row.trackingNumber">
              {{ row.trackingNumber }}
            </p>
          </div>
        </template>

        <template #actions-data="{ row }">
          <div class="flex space-x-2">
            <UButton @click="viewExchange(row)" variant="ghost" size="sm">
              <UIcon name="i-heroicons-eye" class="w-4 h-4" />
            </UButton>
            <UButton
              v-if="row.status === 'pending'"
              @click="approveExchange(row)"
              variant="ghost"
              size="sm"
              color="green"
            >
              <UIcon name="i-heroicons-check" class="w-4 h-4" />
            </UButton>
            <UButton
              v-if="row.status === 'approved'"
              @click="shipExchange(row)"
              variant="ghost"
              size="sm"
              color="blue"
            >
              <UIcon name="i-heroicons-truck" class="w-4 h-4" />
            </UButton>
            <UDropdown :items="getExchangeActions(row)">
              <UButton variant="ghost" size="sm">
                <UIcon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
              </UButton>
            </UDropdown>
          </div>
        </template>
      </UTable>

      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <p class="text-sm text-gray-500">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalRecords) }}
          条，共 {{ totalRecords }} 条记录
        </p>
        <UPagination
          v-model="currentPage"
          :page-count="pageSize"
          :total="totalRecords"
        />
      </div>
    </UCard>

    <!-- 兑换详情模态框 -->
    <UModal v-model="showExchangeModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">兑换详情</h3>
        </template>

        <div v-if="selectedExchange" class="space-y-6">
          <!-- 订单信息 -->
          <div>
            <h4 class="text-md font-semibold text-gray-900 mb-3">订单信息</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">订单号:</span>
                <p class="font-medium text-gray-900">{{ selectedExchange.orderNumber }}</p>
              </div>
              <div>
                <span class="text-gray-500">兑换时间:</span>
                <p class="font-medium text-gray-900">{{ formatDateTime(selectedExchange.createdAt) }}</p>
              </div>
              <div>
                <span class="text-gray-500">状态:</span>
                <UBadge :color="getStatusColor(selectedExchange.status)" variant="soft">
                  {{ getStatusLabel(selectedExchange.status) }}
                </UBadge>
              </div>
              <div>
                <span class="text-gray-500">消费积分:</span>
                <p class="font-medium text-gray-900">{{ selectedExchange.totalPoints }}</p>
              </div>
            </div>
          </div>

          <!-- 员工信息 -->
          <div>
            <h4 class="text-md font-semibold text-gray-900 mb-3">员工信息</h4>
            <div class="flex items-center space-x-3">
              <UAvatar :alt="selectedExchange.employee.name" size="lg" />
              <div>
                <p class="font-medium text-gray-900">{{ selectedExchange.employee.name }}</p>
                <p class="text-sm text-gray-500">{{ selectedExchange.employee.department }}</p>
                <p class="text-sm text-gray-500">{{ selectedExchange.employee.email }}</p>
              </div>
            </div>
          </div>

          <!-- 商品信息 -->
          <div>
            <h4 class="text-md font-semibold text-gray-900 mb-3">商品信息</h4>
            <div class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
              <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-gift" class="w-8 h-8 text-gray-500" />
              </div>
              <div class="flex-1">
                <p class="font-medium text-gray-900">{{ selectedExchange.product.name }}</p>
                <p class="text-sm text-gray-500">数量: {{ selectedExchange.quantity }}</p>
                <p class="text-sm text-gray-500">单价: {{ selectedExchange.product.points }} 积分</p>
              </div>
            </div>
          </div>

          <!-- 配送信息 -->
          <div v-if="selectedExchange.deliveryAddress">
            <h4 class="text-md font-semibold text-gray-900 mb-3">配送信息</h4>
            <div class="space-y-2 text-sm">
              <div>
                <span class="text-gray-500">配送方式:</span>
                <span class="ml-2 text-gray-900">{{ selectedExchange.deliveryMethod }}</span>
              </div>
              <div>
                <span class="text-gray-500">收货地址:</span>
                <p class="text-gray-900">{{ selectedExchange.deliveryAddress }}</p>
              </div>
              <div v-if="selectedExchange.trackingNumber">
                <span class="text-gray-500">快递单号:</span>
                <span class="ml-2 text-gray-900">{{ selectedExchange.trackingNumber }}</span>
              </div>
            </div>
          </div>

          <!-- 备注 -->
          <div v-if="selectedExchange.notes">
            <h4 class="text-md font-semibold text-gray-900 mb-3">备注</h4>
            <p class="text-sm text-gray-600">{{ selectedExchange.notes }}</p>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton @click="showExchangeModal = false" variant="outline">关闭</UButton>
            <UButton
              v-if="selectedExchange?.status === 'pending'"
              @click="approveExchange(selectedExchange)"
              color="green"
            >
              审核通过
            </UButton>
            <UButton
              v-if="selectedExchange?.status === 'approved'"
              @click="shipExchange(selectedExchange)"
              color="blue"
            >
              安排发货
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedStatus = ref('')
const selectedProduct = ref('')
const dateRange = ref({ start: '', end: '' })
const currentPage = ref(1)
const pageSize = ref(10)
const selectedRows = ref([])
const showExchangeModal = ref(false)
const selectedExchange = ref(null)

// 兑换统计数据
const exchangeStats = ref({
  total: 1256,
  completed: 1089,
  pending: 45,
  totalPoints: 89420
})

// 筛选选项
const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '待审核', value: 'pending' },
  { label: '已审核', value: 'approved' },
  { label: '已发货', value: 'shipped' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

const productOptions = [
  { label: '全部商品', value: '' },
  { label: '星巴克咖啡券', value: 'coffee' },
  { label: '无线鼠标', value: 'mouse' },
  { label: '保温杯', value: 'cup' }
]

// 表格列定义
const columns = [
  { key: 'orderNumber', label: '订单号/时间' },
  { key: 'employee', label: '员工信息' },
  { key: 'product', label: '商品信息' },
  { key: 'points', label: '消费积分' },
  { key: 'status', label: '状态' },
  { key: 'deliveryInfo', label: '配送信息' },
  { key: 'actions', label: '操作' }
]

// 模拟兑换记录数据
const exchanges = ref([
  {
    id: 1,
    orderNumber: 'EX202401001',
    employee: {
      name: '张三',
      department: '销售部',
      email: '<EMAIL>'
    },
    product: {
      name: '星巴克咖啡券',
      points: 50
    },
    quantity: 2,
    totalPoints: 100,
    status: 'pending',
    deliveryMethod: '电子兑换券',
    deliveryAddress: '',
    trackingNumber: '',
    notes: '',
    createdAt: new Date('2024-01-15 14:30:00')
  },
  {
    id: 2,
    orderNumber: 'EX202401002',
    employee: {
      name: '李四',
      department: '技术部',
      email: '<EMAIL>'
    },
    product: {
      name: '无线蓝牙鼠标',
      points: 200
    },
    quantity: 1,
    totalPoints: 200,
    status: 'shipped',
    deliveryMethod: '快递配送',
    deliveryAddress: '北京市朝阳区xxx街道xxx号',
    trackingNumber: 'SF1234567890',
    notes: '请在工作时间配送',
    createdAt: new Date('2024-01-14 10:20:00')
  },
  {
    id: 3,
    orderNumber: 'EX202401003',
    employee: {
      name: '王五',
      department: '市场部',
      email: '<EMAIL>'
    },
    product: {
      name: '保温杯',
      points: 150
    },
    quantity: 1,
    totalPoints: 150,
    status: 'completed',
    deliveryMethod: '自提',
    deliveryAddress: '公司前台',
    trackingNumber: '',
    notes: '',
    createdAt: new Date('2024-01-13 16:45:00')
  }
])

// 计算属性
const totalRecords = computed(() => filteredExchanges.value.length)

const filteredExchanges = computed(() => {
  let filtered = exchanges.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(exchange =>
      exchange.orderNumber.toLowerCase().includes(query) ||
      exchange.employee.name.toLowerCase().includes(query) ||
      exchange.product.name.toLowerCase().includes(query)
    )
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(exchange => exchange.status === selectedStatus.value)
  }

  if (selectedProduct.value) {
    // 这里可以根据实际需求过滤商品
  }

  if (dateRange.value.start && dateRange.value.end) {
    const start = new Date(dateRange.value.start)
    const end = new Date(dateRange.value.end)
    filtered = filtered.filter(exchange => {
      const date = new Date(exchange.createdAt)
      return date >= start && date <= end
    })
  }

  return filtered
})

// 方法
const getStatusColor = (status) => {
  const colorMap = {
    'pending': 'yellow',
    'approved': 'blue',
    'shipped': 'purple',
    'completed': 'green',
    'cancelled': 'red'
  }
  return colorMap[status] || 'gray'
}

const getStatusLabel = (status) => {
  const labelMap = {
    'pending': '待审核',
    'approved': '已审核',
    'shipped': '已发货',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return labelMap[status] || status
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

const formatDateTime = (date) => {
  return date.toLocaleString('zh-CN')
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = ''
  selectedProduct.value = ''
  dateRange.value = { start: '', end: '' }
  currentPage.value = 1
}

const exportExchanges = () => {
  console.log('导出兑换记录')
}

const batchProcess = () => {
  console.log('批量处理:', selectedRows.value)
}

const viewExchange = (exchange) => {
  selectedExchange.value = exchange
  showExchangeModal.value = true
}

const approveExchange = (exchange) => {
  console.log('审核通过:', exchange)
  exchange.status = 'approved'
  showExchangeModal.value = false
}

const shipExchange = (exchange) => {
  console.log('安排发货:', exchange)
  exchange.status = 'shipped'
  exchange.trackingNumber = 'SF' + Math.random().toString().substr(2, 10)
  showExchangeModal.value = false
}

const getExchangeActions = (exchange) => {
  const actions = []

  if (exchange.status === 'pending') {
    actions.push([{
      label: '拒绝申请',
      icon: 'i-heroicons-x-mark',
      click: () => rejectExchange(exchange)
    }])
  }

  if (exchange.status === 'shipped') {
    actions.push([{
      label: '标记完成',
      icon: 'i-heroicons-check-circle',
      click: () => completeExchange(exchange)
    }])
  }

  actions.push([{
    label: '取消订单',
    icon: 'i-heroicons-trash',
    click: () => cancelExchange(exchange)
  }])

  return actions
}

const rejectExchange = (exchange) => {
  if (confirm(`确定要拒绝 ${exchange.employee.name} 的兑换申请吗？`)) {
    exchange.status = 'cancelled'
  }
}

const completeExchange = (exchange) => {
  exchange.status = 'completed'
}

const cancelExchange = (exchange) => {
  if (confirm(`确定要取消订单 ${exchange.orderNumber} 吗？`)) {
    exchange.status = 'cancelled'
  }
}
</script>