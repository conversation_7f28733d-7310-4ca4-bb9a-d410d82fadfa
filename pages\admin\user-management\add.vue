<template>
  <div class="add-employee">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">添加员工</h1>
      <p class="text-gray-600">录入新员工基本信息和部门分配</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- 员工信息表单 -->
      <div class="lg:col-span-2">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">基本信息</h3>
          </template>

          <UForm :state="employeeForm" class="space-y-6" @submit="onSubmit">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 基本信息 -->
              <UFormGroup label="姓名" name="name" required>
                <UInput v-model="employeeForm.name" placeholder="请输入员工姓名" />
              </UFormGroup>

              <UFormGroup label="工号" name="employeeId" required>
                <UInput v-model="employeeForm.employeeId" placeholder="请输入工号" />
              </UFormGroup>

              <UFormGroup label="性别" name="gender" required>
                <USelect
                  v-model="employeeForm.gender"
                  :options="genderOptions"
                  placeholder="请选择性别"
                />
              </UFormGroup>

              <UFormGroup label="出生日期" name="birthDate">
                <UInput
                  v-model="employeeForm.birthDate"
                  type="date"
                  placeholder="请选择出生日期"
                />
              </UFormGroup>

              <UFormGroup label="身份证号" name="idCard">
                <UInput v-model="employeeForm.idCard" placeholder="请输入身份证号" />
              </UFormGroup>

              <UFormGroup label="手机号码" name="phone" required>
                <UInput v-model="employeeForm.phone" placeholder="请输入手机号码" />
              </UFormGroup>

              <UFormGroup label="邮箱" name="email" required>
                <UInput v-model="employeeForm.email" type="email" placeholder="请输入邮箱地址" />
              </UFormGroup>

              <UFormGroup label="紧急联系人" name="emergencyContact">
                <UInput v-model="employeeForm.emergencyContact" placeholder="请输入紧急联系人" />
              </UFormGroup>

              <UFormGroup label="紧急联系电话" name="emergencyPhone">
                <UInput v-model="employeeForm.emergencyPhone" placeholder="请输入紧急联系电话" />
              </UFormGroup>

              <UFormGroup label="家庭住址" name="address" class="md:col-span-2">
                <UTextarea v-model="employeeForm.address" placeholder="请输入家庭住址" rows="2" />
              </UFormGroup>
            </div>

            <!-- 工作信息 -->
            <div class="border-t pt-6">
              <h4 class="text-md font-semibold text-gray-900 mb-4">工作信息</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <UFormGroup label="部门" name="department" required>
                  <USelect
                    v-model="employeeForm.department"
                    :options="departmentOptions"
                    placeholder="请选择部门"
                  />
                </UFormGroup>

                <UFormGroup label="职位" name="position" required>
                  <UInput v-model="employeeForm.position" placeholder="请输入职位" />
                </UFormGroup>

                <UFormGroup label="直属上级" name="supervisor">
                  <USelect
                    v-model="employeeForm.supervisor"
                    :options="supervisorOptions"
                    placeholder="请选择直属上级"
                  />
                </UFormGroup>

                <UFormGroup label="入职日期" name="joinDate" required>
                  <UInput
                    v-model="employeeForm.joinDate"
                    type="date"
                    placeholder="请选择入职日期"
                  />
                </UFormGroup>

                <UFormGroup label="员工类型" name="employeeType" required>
                  <USelect
                    v-model="employeeForm.employeeType"
                    :options="employeeTypeOptions"
                    placeholder="请选择员工类型"
                  />
                </UFormGroup>

                <UFormGroup label="工作地点" name="workLocation">
                  <USelect
                    v-model="employeeForm.workLocation"
                    :options="locationOptions"
                    placeholder="请选择工作地点"
                  />
                </UFormGroup>

                <UFormGroup label="基本工资" name="baseSalary">
                  <UInput
                    v-model="employeeForm.baseSalary"
                    type="number"
                    placeholder="请输入基本工资"
                  />
                </UFormGroup>

                <UFormGroup label="试用期(月)" name="probationPeriod">
                  <UInput
                    v-model="employeeForm.probationPeriod"
                    type="number"
                    placeholder="请输入试用期月数"
                  />
                </UFormGroup>
              </div>
            </div>

            <!-- 积分设置 -->
            <div class="border-t pt-6">
              <h4 class="text-md font-semibold text-gray-900 mb-4">积分设置</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <UFormGroup label="初始积分" name="initialPoints">
                  <UInput
                    v-model="employeeForm.initialPoints"
                    type="number"
                    placeholder="请输入初始积分"
                  />
                </UFormGroup>

                <UFormGroup label="积分等级" name="pointsLevel">
                  <USelect
                    v-model="employeeForm.pointsLevel"
                    :options="pointsLevelOptions"
                    placeholder="请选择积分等级"
                  />
                </UFormGroup>
              </div>
            </div>

            <!-- 备注信息 -->
            <div class="border-t pt-6">
              <UFormGroup label="备注" name="notes">
                <UTextarea
                  v-model="employeeForm.notes"
                  placeholder="请输入备注信息..."
                  rows="3"
                />
              </UFormGroup>
            </div>

            <!-- 提交按钮 -->
            <div class="flex justify-end space-x-4 pt-6">
              <UButton @click="resetForm" variant="outline">重置</UButton>
              <UButton @click="saveDraft" variant="outline" color="gray">保存草稿</UButton>
              <UButton type="submit" color="blue" :loading="submitting">
                {{ submitting ? '添加中...' : '添加员工' }}
              </UButton>
            </div>
          </UForm>
        </UCard>
      </div>

      <!-- 侧边栏信息 -->
      <div class="space-y-6">
        <!-- 添加指南 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">添加指南</h3>
          </template>
          <div class="space-y-3 text-sm text-gray-600">
            <div class="flex items-start space-x-2">
              <UIcon name="i-heroicons-information-circle" class="w-4 h-4 mt-0.5 text-blue-500" />
              <p>请确保员工信息准确无误，特别是工号和邮箱</p>
            </div>
            <div class="flex items-start space-x-2">
              <UIcon name="i-heroicons-exclamation-triangle" class="w-4 h-4 mt-0.5 text-yellow-500" />
              <p>工号一旦设定不可修改，请谨慎填写</p>
            </div>
            <div class="flex items-start space-x-2">
              <UIcon name="i-heroicons-check-circle" class="w-4 h-4 mt-0.5 text-green-500" />
              <p>系统会自动为新员工分配初始积分</p>
            </div>
          </div>
        </UCard>

        <!-- 部门统计 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">部门人数统计</h3>
          </template>
          <div class="space-y-3">
            <div v-for="dept in departmentStats" :key="dept.name"
                 class="flex justify-between items-center">
              <span class="text-sm text-gray-600">{{ dept.name }}</span>
              <span class="text-sm font-medium text-gray-900">{{ dept.count }}人</span>
            </div>
          </div>
        </UCard>

        <!-- 最近添加 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">最近添加</h3>
          </template>
          <div class="space-y-3">
            <div v-for="recent in recentEmployees" :key="recent.id"
                 class="flex items-center space-x-3">
              <UAvatar :alt="recent.name" size="sm" />
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">{{ recent.name }}</p>
                <p class="text-xs text-gray-500">{{ recent.department }}</p>
              </div>
              <span class="text-xs text-gray-400">{{ recent.addedTime }}</span>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 响应式数据
const submitting = ref(false)

// 员工表单数据
const employeeForm = ref({
  name: '',
  employeeId: '',
  gender: '',
  birthDate: '',
  idCard: '',
  phone: '',
  email: '',
  emergencyContact: '',
  emergencyPhone: '',
  address: '',
  department: '',
  position: '',
  supervisor: '',
  joinDate: '',
  employeeType: '',
  workLocation: '',
  baseSalary: '',
  probationPeriod: 3,
  initialPoints: 100,
  pointsLevel: '',
  notes: ''
})

// 选项数据
const genderOptions = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
]

const departmentOptions = [
  { label: '销售部', value: 'sales' },
  { label: '技术部', value: 'tech' },
  { label: '市场部', value: 'marketing' },
  { label: '人事部', value: 'hr' },
  { label: '财务部', value: 'finance' }
]

const supervisorOptions = [
  { label: '张经理', value: 'zhang_manager' },
  { label: '李总监', value: 'li_director' },
  { label: '王主管', value: 'wang_supervisor' },
  { label: '赵部长', value: 'zhao_director' }
]

const employeeTypeOptions = [
  { label: '正式员工', value: 'full_time' },
  { label: '试用期员工', value: 'probation' },
  { label: '实习生', value: 'intern' },
  { label: '兼职员工', value: 'part_time' },
  { label: '外包员工', value: 'contractor' }
]

const locationOptions = [
  { label: '总部', value: 'headquarters' },
  { label: '分公司A', value: 'branch_a' },
  { label: '分公司B', value: 'branch_b' },
  { label: '远程办公', value: 'remote' }
]

const pointsLevelOptions = [
  { label: '青铜会员', value: 'bronze' },
  { label: '白银会员', value: 'silver' },
  { label: '黄金会员', value: 'gold' },
  { label: '钻石会员', value: 'diamond' },
  { label: 'VIP会员', value: 'vip' }
]

// 部门统计数据
const departmentStats = ref([
  { name: '销售部', count: 25 },
  { name: '技术部', count: 18 },
  { name: '市场部', count: 15 },
  { name: '人事部', count: 8 },
  { name: '财务部', count: 6 }
])

// 最近添加的员工
const recentEmployees = ref([
  { id: 1, name: '王小明', department: '技术部', addedTime: '2小时前' },
  { id: 2, name: '李小红', department: '销售部', addedTime: '1天前' },
  { id: 3, name: '张小强', department: '市场部', addedTime: '2天前' }
])

// 方法
const onSubmit = async () => {
  submitting.value = true
  try {
    console.log('提交员工信息:', employeeForm.value)
    // 这里会调用API提交数据
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用

    // 提交成功后的处理
    alert('员工添加成功！')
    resetForm()
  } catch (error) {
    console.error('添加员工失败:', error)
    alert('添加员工失败，请重试')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  employeeForm.value = {
    name: '',
    employeeId: '',
    gender: '',
    birthDate: '',
    idCard: '',
    phone: '',
    email: '',
    emergencyContact: '',
    emergencyPhone: '',
    address: '',
    department: '',
    position: '',
    supervisor: '',
    joinDate: '',
    employeeType: '',
    workLocation: '',
    baseSalary: '',
    probationPeriod: 3,
    initialPoints: 100,
    pointsLevel: '',
    notes: ''
  }
}

const saveDraft = () => {
  console.log('保存草稿:', employeeForm.value)
  // 这里会保存草稿到本地存储或服务器
  alert('草稿已保存')
}
</script>