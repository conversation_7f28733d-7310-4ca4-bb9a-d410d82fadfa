<template>
  <div class="admin-layout">
    <aside class="sidebar">
      <h1>凯丽思积分商城 - 后台</h1>
      <nav>
        <ul>
          <li>
            <NuxtLink to="/admin/workbench" class="top-level-link">工作台</NuxtLink>
          </li>

          <li>
            <p>积分管理</p>
            <ul>
              <li><NuxtLink to="/admin/points-management/data">积分数据</NuxtLink></li>
              <li><NuxtLink to="/admin/points-management/settings">积分设置</NuxtLink></li>
            </ul>
          </li>

          <li>
            <p>事件管理</p>
            <ul>
              <li><NuxtLink to="/admin/event-management/settings">活动设置</NuxtLink></li>
            </ul>
          </li>

          <li>
            <p>商品管理</p>
            <ul>
              <li><NuxtLink to="/admin/product-management/add">添加商品，补充商品数据</NuxtLink></li>
              <li><NuxtLink to="/admin/product-management/settings">积分管理</NuxtLink></li>
              <li><NuxtLink to="/admin/product-management/exchange">兑换数据</NuxtLink></li>
            </ul>
          </li>

          <li>
            <p>数据中心</p>
            <ul>
              <li><NuxtLink to="/admin/data-center/points-data">积分数据</NuxtLink></li>
              <li><NuxtLink to="/admin/data-center/user-data">用户数据</NuxtLink></li>
            </ul>
          </li>

          <li>
            <p>人员管理</p>
            <ul>
              <li><NuxtLink to="/admin/user-management/info">员工信息</NuxtLink></li>
              <li><NuxtLink to="/admin/user-management/add">人员添加</NuxtLink></li>
              <li><NuxtLink to="/admin/user-management/departments">部门管理</NuxtLink></li>
            </ul>
          </li>
        </ul>
      </nav>
    </aside>
    <main class="content">
      <slot />
    </main>
  </div>
</template>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: #343a40;
  color: white;
  padding: 1rem;
  flex-shrink: 0;
}

.sidebar h1 {
  font-size: 1.5rem;
  text-align: center;
  margin-bottom: 2rem;
}

.sidebar nav ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sidebar nav > ul > li {
  margin-bottom: 1.5rem;
}

.sidebar nav p {
  margin: 0 0 0.5rem 0;
  font-weight: bold;
  color: #adb5bd;
  font-size: 0.9rem;
  text-transform: uppercase;
  padding-left: 0.5rem;
}

.sidebar nav ul ul {
  list-style-type: none;
  padding-left: 1rem;
}

.sidebar nav a {
  display: block;
  padding: 0.5rem;
  color: #ced4da;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sidebar nav a:hover {
  background-color: #495057;
  color: #fff;
}

.sidebar nav a.router-link-exact-active {
  background-color: #0d6efd;
  color: #fff;
  font-weight: bold;
}

.sidebar .top-level-link {
  font-weight: bold;
  font-size: 1.1rem;
  padding-left: 0.5rem;
}

.content {
  flex-grow: 1;
  padding: 2rem;
  background-color: #f8f9fa;
}
</style> 