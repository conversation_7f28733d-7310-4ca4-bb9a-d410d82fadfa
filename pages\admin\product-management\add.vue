<template>
  <div class="page-container">
    <h2>添加新商品</h2>
    <form @submit.prevent="submitProduct" class="product-form">
      <div class="form-group">
        <label for="product-name">商品名称</label>
        <input type="text" id="product-name" v-model="product.name" required>
      </div>

      <div class="form-group">
        <label for="product-category">商品分类</label>
        <select id="product-category" v-model="product.category" required>
          <option value="" disabled>请选择分类</option>
          <option value="life">生活商品</option>
          <option value="work">工作商品</option>
          <option value="study">学习商品</option>
        </select>
      </div>

      <div class="form-group">
        <label for="product-points">所需积分</label>
        <input type="number" id="product-points" v-model.number="product.points" required min="0">
      </div>

      <div class="form-group">
        <label for="product-stock">库存数量</label>
        <input type="number" id="product-stock" v-model.number="product.stock" required min="0">
      </div>
      
      <div class="form-group">
        <label for="product-description">商品描述</label>
        <textarea id="product-description" v-model="product.description" rows="4"></textarea>
      </div>

      <div class="form-group">
        <label>商品状态</label>
        <div class="radio-group">
          <label><input type="radio" v-model="product.status" value="active" name="status"> 上架</label>
          <label><input type="radio" v-model="product.status" value="inactive" name="status"> 下架</label>
        </div>
      </div>
      
      <button type="submit" class="submit-btn">确认添加</button>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useProducts } from '~/composables/useProducts';
import { useRouter } from 'vue-router';

const { addProduct } = useProducts();
const router = useRouter();

const product = ref({
  name: '',
  category: '',
  points: 0,
  stock: 0,
  description: '',
  image: 'https://via.placeholder.com/150', // Default placeholder
  status: 'active',
});

const submitProduct = () => {
  addProduct({ ...product.value });
  alert('商品已成功添加！');
  // 跳转到商品设置页面以查看结果
  router.push('/admin/product-management/settings');
};
</script>

<style scoped>
.page-container {
  max-width: 800px;
}
h2 {
  margin-bottom: 1.5rem;
}
.product-form {
  background: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.form-group {
  margin-bottom: 1.5rem;
}
.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}
.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.radio-group label {
  margin-right: 1rem;
  font-weight: normal;
}
.submit-btn {
  background-color: #0d6efd;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}
.submit-btn:hover {
  background-color: #0b5ed7;
}
</style>