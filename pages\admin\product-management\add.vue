<template>
  <div class="add-product">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">商品管理</h1>
      <p class="text-gray-600">添加和管理积分商城商品</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- 商品信息表单 -->
      <div class="lg:col-span-2">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">商品信息</h3>
          </template>

          <UForm :state="productForm" class="space-y-6" @submit="onSubmit">
            <!-- 基本信息 -->
            <div class="space-y-4">
              <h4 class="text-md font-semibold text-gray-900">基本信息</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <UFormGroup label="商品名称" name="name" required>
                  <UInput v-model="productForm.name" placeholder="请输入商品名称" />
                </UFormGroup>

                <UFormGroup label="商品编码" name="sku" required>
                  <UInput v-model="productForm.sku" placeholder="请输入商品编码" />
                </UFormGroup>

                <UFormGroup label="商品分类" name="category" required>
                  <USelect
                    v-model="productForm.category"
                    :options="categoryOptions"
                    placeholder="请选择商品分类"
                  />
                </UFormGroup>

                <UFormGroup label="商品品牌" name="brand">
                  <UInput v-model="productForm.brand" placeholder="请输入商品品牌" />
                </UFormGroup>

                <UFormGroup label="兑换积分" name="points" required>
                  <UInput
                    v-model="productForm.points"
                    type="number"
                    placeholder="请输入所需积分"
                  />
                </UFormGroup>

                <UFormGroup label="市场价格" name="marketPrice">
                  <UInput
                    v-model="productForm.marketPrice"
                    type="number"
                    placeholder="请输入市场价格"
                  />
                </UFormGroup>

                <UFormGroup label="库存数量" name="stock" required>
                  <UInput
                    v-model="productForm.stock"
                    type="number"
                    placeholder="请输入库存数量"
                  />
                </UFormGroup>

                <UFormGroup label="商品状态" name="status" required>
                  <USelect
                    v-model="productForm.status"
                    :options="statusOptions"
                    placeholder="请选择商品状态"
                  />
                </UFormGroup>
              </div>

              <UFormGroup label="商品描述" name="description">
                <UTextarea
                  v-model="productForm.description"
                  placeholder="请输入商品描述..."
                  rows="4"
                />
              </UFormGroup>
            </div>

            <!-- 商品图片 -->
            <div class="border-t pt-6">
              <h4 class="text-md font-semibold text-gray-900 mb-4">商品图片</h4>

              <div class="space-y-4">
                <UFormGroup label="主图" name="mainImage">
                  <div class="flex items-center space-x-4">
                    <div class="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                      <UIcon name="i-heroicons-photo" class="w-8 h-8 text-gray-400" />
                    </div>
                    <UButton variant="outline" size="sm">
                      <UIcon name="i-heroicons-cloud-arrow-up" class="w-4 h-4 mr-2" />
                      上传主图
                    </UButton>
                  </div>
                </UFormGroup>

                <UFormGroup label="详情图片" name="detailImages">
                  <div class="grid grid-cols-4 gap-4">
                    <div v-for="i in 4" :key="i"
                         class="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                      <UIcon name="i-heroicons-plus" class="w-6 h-6 text-gray-400" />
                    </div>
                  </div>
                  <p class="text-xs text-gray-500 mt-2">最多上传4张详情图片</p>
                </UFormGroup>
              </div>
            </div>

            <!-- 兑换设置 -->
            <div class="border-t pt-6">
              <h4 class="text-md font-semibold text-gray-900 mb-4">兑换设置</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <UFormGroup label="每人限兑" name="limitPerPerson">
                  <UInput
                    v-model="productForm.limitPerPerson"
                    type="number"
                    placeholder="0表示不限制"
                  />
                </UFormGroup>

                <UFormGroup label="每日限兑" name="limitPerDay">
                  <UInput
                    v-model="productForm.limitPerDay"
                    type="number"
                    placeholder="0表示不限制"
                  />
                </UFormGroup>

                <UFormGroup label="兑换开始时间" name="startTime">
                  <UInput
                    v-model="productForm.startTime"
                    type="datetime-local"
                  />
                </UFormGroup>

                <UFormGroup label="兑换结束时间" name="endTime">
                  <UInput
                    v-model="productForm.endTime"
                    type="datetime-local"
                  />
                </UFormGroup>

                <UFormGroup label="兑换条件" name="exchangeCondition" class="md:col-span-2">
                  <UCheckbox
                    v-model="productForm.requiresApproval"
                    label="需要管理员审核"
                  />
                </UFormGroup>
              </div>
            </div>

            <!-- 配送信息 -->
            <div class="border-t pt-6">
              <h4 class="text-md font-semibold text-gray-900 mb-4">配送信息</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <UFormGroup label="配送方式" name="deliveryMethod" required>
                  <USelect
                    v-model="productForm.deliveryMethod"
                    :options="deliveryOptions"
                    placeholder="请选择配送方式"
                  />
                </UFormGroup>

                <UFormGroup label="配送费用" name="deliveryFee">
                  <UInput
                    v-model="productForm.deliveryFee"
                    type="number"
                    placeholder="0表示免费配送"
                  />
                </UFormGroup>

                <UFormGroup label="预计配送时间" name="deliveryTime">
                  <UInput v-model="productForm.deliveryTime" placeholder="如：3-5个工作日" />
                </UFormGroup>

                <UFormGroup label="配送区域" name="deliveryArea">
                  <USelect
                    v-model="productForm.deliveryArea"
                    :options="areaOptions"
                    placeholder="请选择配送区域"
                  />
                </UFormGroup>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="flex justify-end space-x-4 pt-6">
              <UButton @click="resetForm" variant="outline">重置</UButton>
              <UButton @click="saveDraft" variant="outline" color="gray">保存草稿</UButton>
              <UButton type="submit" color="blue" :loading="submitting">
                {{ submitting ? '添加中...' : '添加商品' }}
              </UButton>
            </div>
          </UForm>
        </UCard>
      </div>

      <!-- 侧边栏信息 -->
      <div class="space-y-6">
        <!-- 商品指南 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">添加指南</h3>
          </template>
          <div class="space-y-3 text-sm text-gray-600">
            <div class="flex items-start space-x-2">
              <UIcon name="i-heroicons-information-circle" class="w-4 h-4 mt-0.5 text-blue-500" />
              <p>商品编码一旦设定不可修改，请谨慎填写</p>
            </div>
            <div class="flex items-start space-x-2">
              <UIcon name="i-heroicons-exclamation-triangle" class="w-4 h-4 mt-0.5 text-yellow-500" />
              <p>建议上传高质量的商品图片以提高兑换率</p>
            </div>
            <div class="flex items-start space-x-2">
              <UIcon name="i-heroicons-check-circle" class="w-4 h-4 mt-0.5 text-green-500" />
              <p>合理设置积分价格，参考市场价值</p>
            </div>
          </div>
        </UCard>

        <!-- 分类统计 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">分类统计</h3>
          </template>
          <div class="space-y-3">
            <div v-for="category in categoryStats" :key="category.name"
                 class="flex justify-between items-center">
              <span class="text-sm text-gray-600">{{ category.name }}</span>
              <span class="text-sm font-medium text-gray-900">{{ category.count }}件</span>
            </div>
          </div>
        </UCard>

        <!-- 热门商品 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">热门商品</h3>
          </template>
          <div class="space-y-3">
            <div v-for="product in popularProducts" :key="product.id"
                 class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <UIcon name="i-heroicons-gift" class="w-5 h-5 text-gray-500" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">{{ product.name }}</p>
                <p class="text-xs text-gray-500">{{ product.points }}积分</p>
              </div>
              <span class="text-xs text-gray-400">{{ product.exchangeCount }}次</span>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 响应式数据
const submitting = ref(false)

// 商品表单数据
const productForm = ref({
  name: '',
  sku: '',
  category: '',
  brand: '',
  points: 0,
  marketPrice: 0,
  stock: 0,
  status: 'active',
  description: '',
  mainImage: '',
  detailImages: [],
  limitPerPerson: 0,
  limitPerDay: 0,
  startTime: '',
  endTime: '',
  requiresApproval: false,
  deliveryMethod: '',
  deliveryFee: 0,
  deliveryTime: '',
  deliveryArea: ''
})

// 选项数据
const categoryOptions = [
  { label: '生活用品', value: 'life' },
  { label: '办公用品', value: 'office' },
  { label: '电子产品', value: 'electronics' },
  { label: '食品饮料', value: 'food' },
  { label: '服装配饰', value: 'clothing' },
  { label: '图书文具', value: 'books' },
  { label: '健康保健', value: 'health' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '上架', value: 'active' },
  { label: '下架', value: 'inactive' },
  { label: '预售', value: 'presale' }
]

const deliveryOptions = [
  { label: '快递配送', value: 'express' },
  { label: '自提', value: 'pickup' },
  { label: '电子兑换券', value: 'voucher' },
  { label: '现场领取', value: 'onsite' }
]

const areaOptions = [
  { label: '全国', value: 'nationwide' },
  { label: '本市', value: 'local' },
  { label: '公司内部', value: 'internal' }
]

// 分类统计数据
const categoryStats = ref([
  { name: '生活用品', count: 15 },
  { name: '办公用品', count: 8 },
  { name: '电子产品', count: 12 },
  { name: '食品饮料', count: 20 },
  { name: '其他', count: 5 }
])

// 热门商品
const popularProducts = ref([
  { id: 1, name: '星巴克咖啡券', points: 50, exchangeCount: 156 },
  { id: 2, name: '无线鼠标', points: 200, exchangeCount: 89 },
  { id: 3, name: '保温杯', points: 150, exchangeCount: 67 }
])

// 方法
const onSubmit = async () => {
  submitting.value = true
  try {
    console.log('提交商品信息:', productForm.value)
    // 这里会调用API提交数据
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用

    // 提交成功后的处理
    alert('商品添加成功！')
    resetForm()
  } catch (error) {
    console.error('添加商品失败:', error)
    alert('添加商品失败，请重试')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  productForm.value = {
    name: '',
    sku: '',
    category: '',
    brand: '',
    points: 0,
    marketPrice: 0,
    stock: 0,
    status: 'active',
    description: '',
    mainImage: '',
    detailImages: [],
    limitPerPerson: 0,
    limitPerDay: 0,
    startTime: '',
    endTime: '',
    requiresApproval: false,
    deliveryMethod: '',
    deliveryFee: 0,
    deliveryTime: '',
    deliveryArea: ''
  }
}

const saveDraft = () => {
  console.log('保存草稿:', productForm.value)
  // 这里会保存草稿到本地存储或服务器
  alert('草稿已保存')
}
</script>