import { useState } from '#app'

export interface Product {
  id: string
  name: string
  category: 'life' | 'work' | 'study' | ''
  points: number
  stock: number
  description: string
  image?: string
  status: 'active' | 'inactive'
}

// 这是响应式的共享状态，将作为我们的临时数据库。
const productsState = useState<Product[]>('products', () => [
  { id: 'P001', name: '品牌笔记本', category: 'work', points: 5000, stock: 50, status: 'active', description: '高性能笔记本电脑', image: 'https://via.placeholder.com/150' },
  { id: 'P002', name: '人体工学鼠标', category: 'work', points: 2500, stock: 120, status: 'active', description: '有效减少手腕疲劳', image: 'https://via.placeholder.com/150' },
  { id: 'P003', name: '机械键盘', category: 'work', points: 4000, stock: 0, status: 'inactive', description: '带 RGB 灯效', image: 'https://via.placeholder.com/150' },
])

export const useProducts = () => {

  const addProduct = (productData: Omit<Product, 'id'>) => {
    productsState.value.push({
      ...productData,
      id: `P${String(Date.now()).slice(-4)}`
    })
  }

  const updateProduct = (updatedProduct: Product) => {
    const index = productsState.value.findIndex(p => p.id === updatedProduct.id)
    if (index !== -1) {
      productsState.value[index] = updatedProduct
    }
  }

  const getProductById = (id: string) => {
    return productsState.value.find(p => p.id === id)
  }

  return {
    products: productsState,
    addProduct,
    updateProduct,
    getProductById,
  }
} 