<template>
  <div class="departments">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">部门管理</h1>
      <p class="text-gray-600">管理公司部门结构和人员分配</p>
    </div>

    <!-- 部门统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-building-office" class="w-6 h-6 text-blue-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ departmentStats.total }}</p>
          <p class="text-sm text-gray-500">总部门数</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-users" class="w-6 h-6 text-green-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ departmentStats.totalEmployees }}</p>
          <p class="text-sm text-gray-500">总员工数</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-user-group" class="w-6 h-6 text-purple-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ departmentStats.avgEmployees }}</p>
          <p class="text-sm text-gray-500">平均人数</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-star" class="w-6 h-6 text-yellow-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ departmentStats.avgPoints }}</p>
          <p class="text-sm text-gray-500">平均积分</p>
        </div>
      </UCard>
    </div>

    <!-- 操作栏 -->
    <UCard class="mb-6">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <UInput 
            v-model="searchQuery" 
            placeholder="搜索部门名称..." 
            icon="i-heroicons-magnifying-glass"
            class="w-64"
          />
          <UButton @click="resetSearch" variant="outline">重置</UButton>
        </div>
        <UButton @click="showAddDepartmentModal = true" color="blue">
          <UIcon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          添加部门
        </UButton>
      </div>
    </UCard>

    <!-- 部门列表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <UCard v-for="department in filteredDepartments" :key="department.id" class="department-card">
        <template #header>
          <div class="flex justify-between items-start">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">{{ department.name }}</h3>
              <p class="text-sm text-gray-500">{{ department.description }}</p>
            </div>
            <UDropdown :items="getDepartmentActions(department)">
              <UButton variant="ghost" size="sm">
                <UIcon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
              </UButton>
            </UDropdown>
          </div>
        </template>

        <div class="space-y-4">
          <!-- 部门信息 -->
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500">部门负责人:</span>
              <p class="font-medium text-gray-900">{{ department.manager || '未设置' }}</p>
            </div>
            <div>
              <span class="text-gray-500">员工人数:</span>
              <p class="font-medium text-gray-900">{{ department.employeeCount }}人</p>
            </div>
            <div>
              <span class="text-gray-500">部门积分:</span>
              <p class="font-medium text-gray-900">{{ department.totalPoints.toLocaleString() }}</p>
            </div>
            <div>
              <span class="text-gray-500">成立时间:</span>
              <p class="font-medium text-gray-900">{{ formatDate(department.createdAt) }}</p>
            </div>
          </div>

          <!-- 员工列表预览 -->
          <div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm font-medium text-gray-700">部门员工</span>
              <UButton @click="viewDepartmentEmployees(department)" variant="ghost" size="xs">
                查看全部
              </UButton>
            </div>
            <div class="space-y-2">
              <div v-for="employee in department.employees.slice(0, 3)" :key="employee.id" 
                   class="flex items-center space-x-2">
                <UAvatar :alt="employee.name" size="xs" />
                <span class="text-xs text-gray-600">{{ employee.name }}</span>
                <span class="text-xs text-gray-400">{{ employee.position }}</span>
              </div>
              <div v-if="department.employees.length > 3" class="text-xs text-gray-400">
                还有 {{ department.employees.length - 3 }} 名员工...
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 添加部门模态框 -->
    <UModal v-model="showAddDepartmentModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">{{ editingDepartment ? '编辑部门' : '添加部门' }}</h3>
        </template>

        <UForm :state="departmentForm" class="space-y-4" @submit="saveDepartment">
          <UFormGroup label="部门名称" name="name" required>
            <UInput v-model="departmentForm.name" placeholder="请输入部门名称" />
          </UFormGroup>

          <UFormGroup label="部门描述" name="description">
            <UTextarea v-model="departmentForm.description" placeholder="请输入部门描述" rows="3" />
          </UFormGroup>

          <UFormGroup label="部门负责人" name="manager">
            <USelect 
              v-model="departmentForm.manager" 
              :options="managerOptions"
              placeholder="请选择部门负责人"
            />
          </UFormGroup>

          <UFormGroup label="上级部门" name="parentDepartment">
            <USelect 
              v-model="departmentForm.parentDepartment" 
              :options="parentDepartmentOptions"
              placeholder="请选择上级部门（可选）"
            />
          </UFormGroup>

          <UFormGroup label="部门职能" name="functions">
            <UTextarea v-model="departmentForm.functions" placeholder="请输入部门主要职能" rows="2" />
          </UFormGroup>
        </UForm>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton @click="closeAddDepartmentModal" variant="outline">取消</UButton>
            <UButton @click="saveDepartment" color="blue" :loading="saving">
              {{ saving ? '保存中...' : '保存' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- 部门员工详情模态框 -->
    <UModal v-model="showEmployeesModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">{{ selectedDepartment?.name }} - 员工列表</h3>
        </template>

        <div v-if="selectedDepartment" class="space-y-4">
          <div class="flex justify-between items-center">
            <p class="text-sm text-gray-600">共 {{ selectedDepartment.employees.length }} 名员工</p>
            <UButton size="sm" variant="outline">
              <UIcon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
              添加员工
            </UButton>
          </div>

          <div class="space-y-3 max-h-96 overflow-y-auto">
            <div v-for="employee in selectedDepartment.employees" :key="employee.id" 
                 class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div class="flex items-center space-x-3">
                <UAvatar :alt="employee.name" size="sm" />
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ employee.name }}</p>
                  <p class="text-xs text-gray-500">{{ employee.position }} | {{ employee.employeeId }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">{{ employee.points.toLocaleString() }}</p>
                <p class="text-xs text-gray-500">积分</p>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end">
            <UButton @click="showEmployeesModal = false" variant="outline">关闭</UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 响应式数据
const searchQuery = ref('')
const showAddDepartmentModal = ref(false)
const showEmployeesModal = ref(false)
const editingDepartment = ref(null)
const selectedDepartment = ref(null)
const saving = ref(false)

// 部门统计
const departmentStats = ref({
  total: 5,
  totalEmployees: 72,
  avgEmployees: 14,
  avgPoints: 1580
})

// 部门表单
const departmentForm = ref({
  name: '',
  description: '',
  manager: '',
  parentDepartment: '',
  functions: ''
})

// 选项数据
const managerOptions = [
  { label: '张经理', value: 'zhang_manager' },
  { label: '李总监', value: 'li_director' },
  { label: '王主管', value: 'wang_supervisor' },
  { label: '赵部长', value: 'zhao_director' }
]

const parentDepartmentOptions = [
  { label: '无上级部门', value: '' },
  { label: '总经理办公室', value: 'ceo_office' },
  { label: '运营中心', value: 'operation_center' }
]

// 模拟部门数据
const departments = ref([
  {
    id: 1,
    name: '销售部',
    description: '负责公司产品销售和客户关系维护',
    manager: '张经理',
    employeeCount: 25,
    totalPoints: 12500,
    createdAt: new Date('2020-01-15'),
    employees: [
      { id: 1, name: '张三', position: '销售经理', employeeId: 'EMP001', points: 2580 },
      { id: 2, name: '李四', position: '销售专员', employeeId: 'EMP002', points: 1850 },
      { id: 3, name: '王五', position: '销售助理', employeeId: 'EMP003', points: 1200 },
      { id: 4, name: '赵六', position: '客户经理', employeeId: 'EMP004', points: 2100 }
    ]
  },
  {
    id: 2,
    name: '技术部',
    description: '负责产品研发和技术支持',
    manager: '李总监',
    employeeCount: 18,
    totalPoints: 11200,
    createdAt: new Date('2020-02-20'),
    employees: [
      { id: 5, name: '钱七', position: '技术总监', employeeId: 'EMP005', points: 3200 },
      { id: 6, name: '孙八', position: '前端工程师', employeeId: 'EMP006', points: 2400 },
      { id: 7, name: '周九', position: '后端工程师', employeeId: 'EMP007', points: 2800 }
    ]
  },
  {
    id: 3,
    name: '市场部',
    description: '负责市场推广和品牌建设',
    manager: '王主管',
    employeeCount: 15,
    totalPoints: 9800,
    createdAt: new Date('2020-03-10'),
    employees: [
      { id: 8, name: '吴十', position: '市场经理', employeeId: 'EMP008', points: 2200 },
      { id: 9, name: '郑十一', position: '品牌专员', employeeId: 'EMP009', points: 1800 }
    ]
  },
  {
    id: 4,
    name: '人事部',
    description: '负责人力资源管理和员工关系',
    manager: '赵部长',
    employeeCount: 8,
    totalPoints: 6400,
    createdAt: new Date('2020-01-05'),
    employees: [
      { id: 10, name: '冯十二', position: '人事经理', employeeId: 'EMP010', points: 2000 },
      { id: 11, name: '陈十三', position: '招聘专员', employeeId: 'EMP011', points: 1500 }
    ]
  },
  {
    id: 5,
    name: '财务部',
    description: '负责财务管理和成本控制',
    manager: '钱总',
    employeeCount: 6,
    totalPoints: 4200,
    createdAt: new Date('2020-01-08'),
    employees: [
      { id: 12, name: '卫十四', position: '财务经理', employeeId: 'EMP012', points: 1800 },
      { id: 13, name: '蒋十五', position: '会计', employeeId: 'EMP013', points: 1400 }
    ]
  }
])

// 计算属性
const filteredDepartments = computed(() => {
  if (!searchQuery.value) {
    return departments.value
  }
  return departments.value.filter(dept =>
    dept.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    dept.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

const resetSearch = () => {
  searchQuery.value = ''
}

const getDepartmentActions = (department) => {
  return [
    [{
      label: '查看员工',
      icon: 'i-heroicons-users',
      click: () => viewDepartmentEmployees(department)
    }],
    [{
      label: '编辑部门',
      icon: 'i-heroicons-pencil',
      click: () => editDepartment(department)
    }],
    [{
      label: '删除部门',
      icon: 'i-heroicons-trash',
      click: () => deleteDepartment(department)
    }]
  ]
}

const viewDepartmentEmployees = (department) => {
  selectedDepartment.value = department
  showEmployeesModal.value = true
}

const editDepartment = (department) => {
  editingDepartment.value = department
  departmentForm.value = {
    name: department.name,
    description: department.description,
    manager: department.manager,
    parentDepartment: '',
    functions: ''
  }
  showAddDepartmentModal.value = true
}

const deleteDepartment = (department) => {
  if (confirm(`确定要删除 ${department.name} 吗？`)) {
    const index = departments.value.findIndex(d => d.id === department.id)
    if (index > -1) {
      departments.value.splice(index, 1)
    }
  }
}

const saveDepartment = async () => {
  saving.value = true
  try {
    if (editingDepartment.value) {
      // 编辑部门
      const index = departments.value.findIndex(d => d.id === editingDepartment.value.id)
      if (index > -1) {
        departments.value[index] = {
          ...departments.value[index],
          ...departmentForm.value
        }
      }
    } else {
      // 添加新部门
      const newDepartment = {
        id: Math.max(...departments.value.map(d => d.id)) + 1,
        ...departmentForm.value,
        employeeCount: 0,
        totalPoints: 0,
        createdAt: new Date(),
        employees: []
      }
      departments.value.push(newDepartment)
    }

    closeAddDepartmentModal()
  } catch (error) {
    console.error('保存部门失败:', error)
  } finally {
    saving.value = false
  }
}

const closeAddDepartmentModal = () => {
  showAddDepartmentModal.value = false
  editingDepartment.value = null
  departmentForm.value = {
    name: '',
    description: '',
    manager: '',
    parentDepartment: '',
    functions: ''
  }
}
</script>
