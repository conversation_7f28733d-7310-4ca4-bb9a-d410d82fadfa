# 积分商城后台系统

## 项目描述

这是一个使用 Nuxt.js 构建的积分商城后台系统的前端应用程序。

## 功能模块

此项目的前端页面已根据需求文档完成搭建，并分为前台和后台两个部分。

### 前台 (用户端)
- **首页**: 包含积分排名、热门兑换商品和限时活动等内容。
- **商城**: 展示可兑换的商品列表。
- **我的积分**: 显示用户的个人积分信息、等级和兑换记录。
- **积分规则**: 详细说明积分的获取和等级规则。

### 后台 (管理端)
通过 `/admin` 路径访问，包含以下模块：

- **工作台**: 后台首页，提供核心数据概览。
- **积分管理**:
  - `积分数据`: 查看和管理用户的积分流水。
  - `积分设置`: 配置加减分规则和用户等级。
- **事件管理**:
  - `活动设置`: 创建和管理积分活动及奖品。
- **商品管理**:
  - `添加商品`: 添加新商品并录入其名称、分类、所需积分、库存等详细数据。
  - `积分管理`: 集中查看和修改所有商品的兑换积分。
  - `兑换数据`: 查看用户的商品兑换记录。
- **数据中心**:
  - `积分数据`: 从全局视角分析积分的增减情况。
  - `用户数据`: 分析新老员工的积分获取情况及用户画像。
- **用户管理**:
  - `基础信息`: 查看和管理所有用户的账户信息。
  - `人员添加`: 手动添加新用户到系统。

## 未来工作

- 实现后端 API 对接。
- 为数据分析页面添加图表（如柱状图、饼图）。
- 完善前端的用户体验和交互细节。
- 添加后台操作的权限控制。

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## 配置

### 字体

由于众所周知的网络原因，项目在初始化时可能会遇到从 Google Fonts 拉取字体超时的问题。为了解决这个问题，已在 `nuxt.config.ts` 中将字体提供商设置为 `none`，禁用了在线字体获取。如果需要使用自定义在线字体，请修改此配置并确保网络连接正常。

## 项目结构

本项目的入口文件是 `app.vue`，它负责加载 `<NuxtLayout />` 和 `<NuxtPage />`。

应用分为 **前台** 和 **后台** 两部分，通过布局和路由进行分离。

### 布局 (Layouts)
- **`layouts/default.vue`**: 前台布局，用于面向普通用户的页面，如首页、商城、我的积分等。
- **`layouts/admin.vue`**: 后台布局，用于所有管理页面。

### 页面 (Pages)
- **前台页面**: 直接位于 `pages` 目录下，例如 `pages/index.vue`, `pages/mall/`, `pages/my-points/`。
- **后台页面**: 全部位于 `pages/admin/` 目录下，通过 `/admin/*` 路径访问。

### 中间件 (Middleware)
- **`middleware/layout.global.js`**: 全局中间件，根据路由路径 (`/admin` 或其他) 自动为页面设置 `admin` 或 `default` 布局。
- **`middleware/redirect.global.js`**: 全局中间件，当访问 `/admin` 时，自动重定向到 `/admin/workbench`。
