<template>
  <div>
    <h2>商城</h2>
    <p v-if="!activeProducts.length">暂无商品上架。</p>
    <div v-else class="product-grid">
      <div v-for="product in activeProducts" :key="product.id" class="product-card">
        <img :src="product.image" :alt="product.name" class="product-image">
        <div class="product-info">
          <h3>{{ product.name }}</h3>
          <p class="product-points">积分: {{ product.points }}</p>
          <p class="product-stock">剩余库存: {{ product.stock }}</p>
          <button class="exchange-btn">立即兑换</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useProducts } from '~/composables/useProducts';

const { products } = useProducts();

// 只在商城显示状态为"上架中"的商品
const activeProducts = computed(() => products.value.filter(p => p.status === 'active'));
</script>

<style scoped>
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}
.product-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}
.product-card:hover {
  transform: translateY(-5px);
}
.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}
.product-info {
  padding: 1rem;
}
.product-info h3 {
  margin: 0 0 0.5rem 0;
}
.product-points {
  font-weight: bold;
  color: #d9534f;
  margin: 0.5rem 0;
}
.product-stock {
  font-size: 0.9rem;
  color: #777;
}
.exchange-btn {
  width: 100%;
  padding: 0.75rem;
  margin-top: 1rem;
  background-color: #0d6efd;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
