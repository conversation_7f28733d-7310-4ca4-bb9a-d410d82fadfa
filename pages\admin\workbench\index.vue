<template>
  <div class="workbench-container">
    <div class="header">
      <h1>工作台</h1>
      <p>凯丽思积分商城管理后台</p>
    </div>

    <!-- 数据概览卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-icon blue">👥</div>
          <div class="stats-info">
            <p class="stats-label">总员工数</p>
            <p class="stats-value">{{ stats.totalEmployees }}</p>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-icon green">💰</div>
          <div class="stats-info">
            <p class="stats-label">总积分发放</p>
            <p class="stats-value">{{ stats.totalPointsIssued.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-icon purple">🎁</div>
          <div class="stats-info">
            <p class="stats-label">商品总数</p>
            <p class="stats-value">{{ stats.totalProducts }}</p>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-icon orange">📈</div>
          <div class="stats-info">
            <p class="stats-label">本月兑换</p>
            <p class="stats-value">{{ stats.monthlyExchanges }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 快捷操作 -->
      <div class="card">
        <h3>快捷操作</h3>
        <div class="quick-actions">
          <NuxtLink to="/admin/user-management/add" class="action-btn blue">
            <span class="action-icon">👤</span>
            添加员工
          </NuxtLink>
          <NuxtLink to="/admin/product-management/add" class="action-btn green">
            <span class="action-icon">➕</span>
            添加商品
          </NuxtLink>
          <NuxtLink to="/admin/points-management/settings" class="action-btn purple">
            <span class="action-icon">⚙️</span>
            积分设置
          </NuxtLink>
          <NuxtLink to="/admin/user-management/info" class="action-btn orange">
            <span class="action-icon">🏢</span>
            用户管理
          </NuxtLink>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="card">
        <h3>最近活动</h3>
        <div class="activities">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon" :class="activity.type">{{ activity.emoji }}</div>
            <div class="activity-content">
              <p class="activity-desc">{{ activity.description }}</p>
              <p class="activity-time">{{ activity.time }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 部门积分排行 -->
    <div class="card">
      <h3>部门积分排行</h3>
      <div class="ranking">
        <div v-for="(dept, index) in departmentRanking" :key="dept.id" class="ranking-item">
          <div class="ranking-position" :class="{ 'top-three': index < 3 }">
            {{ index + 1 }}
          </div>
          <div class="ranking-info">
            <p class="dept-name">{{ dept.name }}</p>
            <p class="dept-count">{{ dept.employeeCount }} 名员工</p>
          </div>
          <div class="ranking-points">
            <p class="points-value">{{ dept.totalPoints.toLocaleString() }}</p>
            <p class="points-label">积分</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 模拟数据
const stats = ref({
  totalEmployees: 156,
  totalPointsIssued: 45680,
  totalProducts: 23,
  monthlyExchanges: 89
})

const recentActivities = ref([
  {
    id: 1,
    description: '张三兑换了咖啡券',
    time: '2分钟前',
    emoji: '🎁',
    type: 'exchange'
  },
  {
    id: 2,
    description: '李四获得了50积分',
    time: '5分钟前',
    emoji: '➕',
    type: 'points'
  },
  {
    id: 3,
    description: '新增商品：无线耳机',
    time: '10分钟前',
    emoji: '🛍️',
    type: 'product'
  },
  {
    id: 4,
    description: '王五加入了技术部',
    time: '1小时前',
    emoji: '👤',
    type: 'user'
  }
])

const departmentRanking = ref([
  { id: 1, name: '销售部', employeeCount: 25, totalPoints: 12500 },
  { id: 2, name: '技术部', employeeCount: 18, totalPoints: 11200 },
  { id: 3, name: '市场部', employeeCount: 15, totalPoints: 9800 },
  { id: 4, name: '人事部', employeeCount: 8, totalPoints: 6400 },
  { id: 5, name: '财务部', employeeCount: 6, totalPoints: 4200 }
])
</script>

<style scoped>
.workbench-container {
  padding: 0;
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.header p {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stats-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
}

.stats-icon.blue { background-color: #dbeafe; }
.stats-icon.green { background-color: #d1fae5; }
.stats-icon.purple { background-color: #e9d5ff; }
.stats-icon.orange { background-color: #fed7aa; }

.stats-info {
  flex: 1;
}

.stats-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 0.25rem 0;
}

.stats-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

.card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0.75rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
}

.action-btn.blue { background-color: #dbeafe; color: #1e40af; }
.action-btn.green { background-color: #d1fae5; color: #065f46; }
.action-btn.purple { background-color: #e9d5ff; color: #7c2d12; }
.action-btn.orange { background-color: #fed7aa; color: #9a3412; }

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.activities {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.activity-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  font-size: 1rem;
}

.activity-content {
  flex: 1;
}

.activity-desc {
  font-size: 0.875rem;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.activity-time {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.ranking {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ranking-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ranking-position {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #1f2937;
}

.ranking-position.top-three {
  background-color: #fef3c7;
  color: #92400e;
}

.ranking-info {
  flex: 1;
  margin-left: 0.75rem;
}

.dept-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.dept-count {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.ranking-points {
  text-align: right;
}

.points-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.points-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}
</style>