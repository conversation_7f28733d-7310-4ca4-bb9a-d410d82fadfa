<template>
  <div class="employee-info">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">员工信息</h1>
      <p class="text-gray-600">管理员工基本信息、部门分配和积分状态</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-users" class="w-6 h-6 text-blue-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ employeeStats.total }}</p>
          <p class="text-sm text-gray-500">总员工数</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-user-plus" class="w-6 h-6 text-green-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ employeeStats.active }}</p>
          <p class="text-sm text-gray-500">在职员工</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-building-office" class="w-6 h-6 text-yellow-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ employeeStats.departments }}</p>
          <p class="text-sm text-gray-500">部门数量</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-star" class="w-6 h-6 text-purple-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ employeeStats.avgPoints }}</p>
          <p class="text-sm text-gray-500">平均积分</p>
        </div>
      </UCard>
    </div>

    <!-- 筛选和搜索 -->
    <UCard class="mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索员工姓名、工号或邮箱..."
            icon="i-heroicons-magnifying-glass"
          />
        </div>
        <div class="flex gap-2">
          <USelect
            v-model="selectedDepartment"
            :options="departmentOptions"
            placeholder="部门"
          />
          <USelect
            v-model="selectedStatus"
            :options="statusOptions"
            placeholder="状态"
          />
          <UButton @click="resetFilters" variant="outline">重置</UButton>
          <UButton @click="exportEmployees" variant="outline">
            <UIcon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-2" />
            导出
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- 员工列表 -->
    <UCard>
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">员工列表</h3>
          <UButton to="/admin/user-management/add" color="blue">
            <UIcon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
            添加员工
          </UButton>
        </div>
      </template>

      <UTable
        :rows="filteredEmployees"
        :columns="columns"
        :loading="loading"
      >
        <template #employee-data="{ row }">
          <div class="flex items-center space-x-3">
            <UAvatar :alt="row.name" size="sm" />
            <div>
              <p class="text-sm font-medium text-gray-900">{{ row.name }}</p>
              <p class="text-xs text-gray-500">{{ row.employeeId }}</p>
            </div>
          </div>
        </template>

        <template #department-data="{ row }">
          <div>
            <p class="text-sm text-gray-900">{{ row.department }}</p>
            <p class="text-xs text-gray-500">{{ row.position }}</p>
          </div>
        </template>

        <template #points-data="{ row }">
          <div class="text-center">
            <p class="text-sm font-semibold text-gray-900">{{ row.points.toLocaleString() }}</p>
            <UBadge
              :color="getPointsLevelColor(row.pointsLevel)"
              variant="soft"
              size="xs"
            >
              {{ row.pointsLevel }}
            </UBadge>
          </div>
        </template>

        <template #status-data="{ row }">
          <UBadge
            :color="getStatusColor(row.status)"
            variant="soft"
          >
            {{ getStatusLabel(row.status) }}
          </UBadge>
        </template>

        <template #joinDate-data="{ row }">
          <div>
            <p class="text-sm text-gray-900">{{ formatDate(row.joinDate) }}</p>
            <p class="text-xs text-gray-500">{{ getWorkYears(row.joinDate) }}年工龄</p>
          </div>
        </template>

        <template #actions-data="{ row }">
          <div class="flex space-x-2">
            <UButton @click="viewEmployee(row)" variant="ghost" size="sm">
              <UIcon name="i-heroicons-eye" class="w-4 h-4" />
            </UButton>
            <UButton @click="editEmployee(row)" variant="ghost" size="sm">
              <UIcon name="i-heroicons-pencil" class="w-4 h-4" />
            </UButton>
            <UButton @click="adjustPoints(row)" variant="ghost" size="sm" color="green">
              <UIcon name="i-heroicons-currency-dollar" class="w-4 h-4" />
            </UButton>
          </div>
        </template>
      </UTable>

      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <p class="text-sm text-gray-500">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalRecords) }}
          条，共 {{ totalRecords }} 条记录
        </p>
        <UPagination
          v-model="currentPage"
          :page-count="pageSize"
          :total="totalRecords"
        />
      </div>
    </UCard>

    <!-- 员工详情模态框 -->
    <UModal v-model="showEmployeeModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">员工详情</h3>
        </template>

        <div v-if="selectedEmployee" class="space-y-6">
          <div class="flex items-center space-x-4">
            <UAvatar :alt="selectedEmployee.name" size="lg" />
            <div>
              <h4 class="text-xl font-semibold text-gray-900">{{ selectedEmployee.name }}</h4>
              <p class="text-gray-500">{{ selectedEmployee.employeeId }}</p>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">部门</label>
              <p class="text-gray-900">{{ selectedEmployee.department }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">职位</label>
              <p class="text-gray-900">{{ selectedEmployee.position }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">邮箱</label>
              <p class="text-gray-900">{{ selectedEmployee.email }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">手机</label>
              <p class="text-gray-900">{{ selectedEmployee.phone }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">入职时间</label>
              <p class="text-gray-900">{{ formatDate(selectedEmployee.joinDate) }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">当前积分</label>
              <p class="text-gray-900">{{ selectedEmployee.points.toLocaleString() }}</p>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton @click="showEmployeeModal = false" variant="outline">关闭</UButton>
            <UButton @click="editEmployee(selectedEmployee)" color="blue">编辑</UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- 积分调整模态框 -->
    <UModal v-model="showPointsModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">积分调整</h3>
        </template>

        <div v-if="selectedEmployee" class="space-y-4">
          <div>
            <p class="text-sm text-gray-500">员工：{{ selectedEmployee.name }}</p>
            <p class="text-sm text-gray-500">当前积分：{{ selectedEmployee.points.toLocaleString() }}</p>
          </div>

          <UFormGroup label="调整类型">
            <URadioGroup
              v-model="pointsAdjustment.type"
              :options="[
                { label: '增加积分', value: 'add' },
                { label: '扣除积分', value: 'subtract' },
                { label: '设置积分', value: 'set' }
              ]"
            />
          </UFormGroup>

          <UFormGroup label="积分数量">
            <UInput
              v-model="pointsAdjustment.amount"
              type="number"
              placeholder="输入积分数量"
            />
          </UFormGroup>

          <UFormGroup label="调整原因">
            <UTextarea
              v-model="pointsAdjustment.reason"
              placeholder="请输入调整原因..."
              rows="3"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton @click="showPointsModal = false" variant="outline">取消</UButton>
            <UButton @click="confirmPointsAdjustment" color="blue">确认调整</UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedDepartment = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showEmployeeModal = ref(false)
const showPointsModal = ref(false)
const selectedEmployee = ref(null)

// 员工统计数据
const employeeStats = ref({
  total: 156,
  active: 148,
  departments: 5,
  avgPoints: 1250
})

// 积分调整数据
const pointsAdjustment = ref({
  type: 'add',
  amount: 0,
  reason: ''
})

// 筛选选项
const departmentOptions = [
  { label: '全部部门', value: '' },
  { label: '销售部', value: 'sales' },
  { label: '技术部', value: 'tech' },
  { label: '市场部', value: 'marketing' },
  { label: '人事部', value: 'hr' },
  { label: '财务部', value: 'finance' }
]

const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '在职', value: 'active' },
  { label: '离职', value: 'inactive' },
  { label: '试用期', value: 'probation' }
]

// 表格列定义
const columns = [
  { key: 'employee', label: '员工信息' },
  { key: 'department', label: '部门/职位' },
  { key: 'points', label: '积分/等级' },
  { key: 'status', label: '状态' },
  { key: 'joinDate', label: '入职时间' },
  { key: 'actions', label: '操作' }
]

// 模拟员工数据
const employees = ref([
  {
    id: 1,
    name: '张三',
    employeeId: 'EMP001',
    department: '销售部',
    position: '销售经理',
    email: '<EMAIL>',
    phone: '13800138001',
    points: 2580,
    pointsLevel: '黄金会员',
    status: 'active',
    joinDate: new Date('2022-03-15'),
    avatar: ''
  },
  {
    id: 2,
    name: '李四',
    employeeId: 'EMP002',
    department: '技术部',
    position: '前端工程师',
    email: '<EMAIL>',
    phone: '13800138002',
    points: 1850,
    pointsLevel: '白银会员',
    status: 'active',
    joinDate: new Date('2021-08-20'),
    avatar: ''
  },
  {
    id: 3,
    name: '王五',
    employeeId: 'EMP003',
    department: '市场部',
    position: '市场专员',
    email: '<EMAIL>',
    phone: '13800138003',
    points: 3200,
    pointsLevel: '黄金会员',
    status: 'active',
    joinDate: new Date('2020-12-10'),
    avatar: ''
  },
  {
    id: 4,
    name: '赵六',
    employeeId: 'EMP004',
    department: '人事部',
    position: '人事专员',
    email: '<EMAIL>',
    phone: '13800138004',
    points: 1200,
    pointsLevel: '白银会员',
    status: 'probation',
    joinDate: new Date('2024-01-08'),
    avatar: ''
  },
  {
    id: 5,
    name: '钱七',
    employeeId: 'EMP005',
    department: '财务部',
    position: '会计',
    email: '<EMAIL>',
    phone: '13800138005',
    points: 4500,
    pointsLevel: '钻石会员',
    status: 'active',
    joinDate: new Date('2019-06-25'),
    avatar: ''
  }
])

// 计算属性
const totalRecords = computed(() => filteredEmployees.value.length)

const filteredEmployees = computed(() => {
  let filtered = employees.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(emp =>
      emp.name.toLowerCase().includes(query) ||
      emp.employeeId.toLowerCase().includes(query) ||
      emp.email.toLowerCase().includes(query)
    )
  }

  if (selectedDepartment.value) {
    const deptMap = {
      'sales': '销售部',
      'tech': '技术部',
      'marketing': '市场部',
      'hr': '人事部',
      'finance': '财务部'
    }
    filtered = filtered.filter(emp => emp.department === deptMap[selectedDepartment.value])
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(emp => emp.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const getPointsLevelColor = (level) => {
  const colorMap = {
    '青铜会员': 'orange',
    '白银会员': 'gray',
    '黄金会员': 'yellow',
    '钻石会员': 'blue',
    'VIP会员': 'purple'
  }
  return colorMap[level] || 'gray'
}

const getStatusColor = (status) => {
  const colorMap = {
    'active': 'green',
    'inactive': 'red',
    'probation': 'yellow'
  }
  return colorMap[status] || 'gray'
}

const getStatusLabel = (status) => {
  const labelMap = {
    'active': '在职',
    'inactive': '离职',
    'probation': '试用期'
  }
  return labelMap[status] || status
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

const getWorkYears = (joinDate) => {
  const years = (new Date() - joinDate) / (1000 * 60 * 60 * 24 * 365)
  return Math.floor(years)
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedDepartment.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}

const exportEmployees = () => {
  console.log('导出员工数据')
  // 这里会实现导出功能
}

const viewEmployee = (employee) => {
  selectedEmployee.value = employee
  showEmployeeModal.value = true
}

const editEmployee = (employee) => {
  // 跳转到编辑页面或打开编辑模态框
  console.log('编辑员工:', employee)
}

const adjustPoints = (employee) => {
  selectedEmployee.value = employee
  pointsAdjustment.value = {
    type: 'add',
    amount: 0,
    reason: ''
  }
  showPointsModal.value = true
}

const confirmPointsAdjustment = () => {
  console.log('积分调整:', {
    employee: selectedEmployee.value,
    adjustment: pointsAdjustment.value
  })
  // 这里会调用API进行积分调整
  showPointsModal.value = false
}
</script>