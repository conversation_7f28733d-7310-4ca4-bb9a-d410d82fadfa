{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "^1.4.1", "@nuxt/image": "^1.10.0", "@nuxt/ui": "^3.1.3", "@nuxtjs/tailwindcss": "^7.0.0-beta.0", "eslint": "^9.28.0", "nuxt": "^3.17.5", "typescript": "^5.8.3", "vue": "^3.5.16", "vue-router": "^4.5.1"}}