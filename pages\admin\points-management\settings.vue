<template>
  <div class="points-settings">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">积分设置</h1>
      <p class="text-gray-600">配置积分获取规则和系统参数</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 积分获取规则 -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900">积分获取规则</h3>
        </template>

        <UForm :state="pointRules" class="space-y-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">每日签到</label>
              <div class="flex items-center space-x-4">
                <UInput
                  v-model="pointRules.dailyCheckin"
                  type="number"
                  placeholder="积分数量"
                  class="flex-1"
                />
                <span class="text-sm text-gray-500">积分/次</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">完成任务</label>
              <div class="flex items-center space-x-4">
                <UInput
                  v-model="pointRules.taskCompletion"
                  type="number"
                  placeholder="积分数量"
                  class="flex-1"
                />
                <span class="text-sm text-gray-500">积分/任务</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">销售业绩</label>
              <div class="flex items-center space-x-4">
                <UInput
                  v-model="pointRules.salesPerformance"
                  type="number"
                  placeholder="积分比例"
                  class="flex-1"
                />
                <span class="text-sm text-gray-500">积分/万元</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">团队协作</label>
              <div class="flex items-center space-x-4">
                <UInput
                  v-model="pointRules.teamwork"
                  type="number"
                  placeholder="积分数量"
                  class="flex-1"
                />
                <span class="text-sm text-gray-500">积分/次</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">培训学习</label>
              <div class="flex items-center space-x-4">
                <UInput
                  v-model="pointRules.training"
                  type="number"
                  placeholder="积分数量"
                  class="flex-1"
                />
                <span class="text-sm text-gray-500">积分/课程</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">创新建议</label>
              <div class="flex items-center space-x-4">
                <UInput
                  v-model="pointRules.innovation"
                  type="number"
                  placeholder="积分数量"
                  class="flex-1"
                />
                <span class="text-sm text-gray-500">积分/建议</span>
              </div>
            </div>
          </div>

          <UButton @click="savePointRules" color="blue" class="w-full">
            保存积分规则
          </UButton>
        </UForm>
      </UCard>

      <!-- 系统设置 -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900">系统设置</h3>
        </template>

        <UForm :state="systemSettings" class="space-y-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">积分有效期</label>
              <USelect
                v-model="systemSettings.pointExpiry"
                :options="expiryOptions"
                placeholder="选择有效期"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">每日最大获取积分</label>
              <UInput
                v-model="systemSettings.dailyMaxPoints"
                type="number"
                placeholder="输入最大积分数"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">积分兑换比例</label>
              <div class="flex items-center space-x-2">
                <UInput
                  v-model="systemSettings.exchangeRatio"
                  type="number"
                  placeholder="比例"
                  class="flex-1"
                />
                <span class="text-sm text-gray-500">积分 = 1元</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">自动发放设置</label>
              <div class="space-y-3">
                <UCheckbox
                  v-model="systemSettings.autoIssue.birthday"
                  label="生日自动发放积分"
                />
                <UCheckbox
                  v-model="systemSettings.autoIssue.anniversary"
                  label="入职周年自动发放积分"
                />
                <UCheckbox
                  v-model="systemSettings.autoIssue.holiday"
                  label="节假日自动发放积分"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">积分清零规则</label>
              <URadioGroup
                v-model="systemSettings.clearRule"
                :options="clearRuleOptions"
              />
            </div>
          </div>

          <UButton @click="saveSystemSettings" color="green" class="w-full">
            保存系统设置
          </UButton>
        </UForm>
      </UCard>
    </div>

    <!-- 积分等级设置 -->
    <UCard class="mt-8">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">积分等级设置</h3>
          <UButton @click="addLevel" variant="outline" size="sm">
            <UIcon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
            添加等级
          </UButton>
        </div>
      </template>

      <div class="space-y-4">
        <div v-for="(level, index) in pointLevels" :key="level.id"
             class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
          <div class="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
            <UInput
              v-model="level.name"
              placeholder="等级名称"
            />
            <UInput
              v-model="level.minPoints"
              type="number"
              placeholder="最低积分"
            />
            <UInput
              v-model="level.maxPoints"
              type="number"
              placeholder="最高积分"
            />
            <UInput
              v-model="level.benefits"
              placeholder="等级权益"
            />
          </div>
          <UButton
            @click="removeLevel(index)"
            color="red"
            variant="ghost"
            size="sm"
          >
            <UIcon name="i-heroicons-trash" class="w-4 h-4" />
          </UButton>
        </div>
      </div>

      <div class="mt-6">
        <UButton @click="savePointLevels" color="purple" class="w-full">
          保存等级设置
        </UButton>
      </div>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 积分规则设置
const pointRules = ref({
  dailyCheckin: 10,
  taskCompletion: 50,
  salesPerformance: 100,
  teamwork: 30,
  training: 80,
  innovation: 200
})

// 系统设置
const systemSettings = ref({
  pointExpiry: '365',
  dailyMaxPoints: 500,
  exchangeRatio: 100,
  autoIssue: {
    birthday: true,
    anniversary: true,
    holiday: false
  },
  clearRule: 'never'
})

// 积分等级
const pointLevels = ref([
  { id: 1, name: '青铜会员', minPoints: 0, maxPoints: 999, benefits: '基础权益' },
  { id: 2, name: '白银会员', minPoints: 1000, maxPoints: 2999, benefits: '9.5折优惠' },
  { id: 3, name: '黄金会员', minPoints: 3000, maxPoints: 4999, benefits: '9折优惠' },
  { id: 4, name: '钻石会员', minPoints: 5000, maxPoints: 9999, benefits: '8.5折优惠' },
  { id: 5, name: 'VIP会员', minPoints: 10000, maxPoints: 999999, benefits: '8折优惠+专属客服' }
])

// 选项数据
const expiryOptions = [
  { label: '永不过期', value: 'never' },
  { label: '1年', value: '365' },
  { label: '2年', value: '730' },
  { label: '3年', value: '1095' }
]

const clearRuleOptions = [
  { label: '永不清零', value: 'never' },
  { label: '每年清零', value: 'yearly' },
  { label: '离职时清零', value: 'resignation' }
]

// 方法
const savePointRules = () => {
  console.log('保存积分规则:', pointRules.value)
  // 这里会调用API保存数据
}

const saveSystemSettings = () => {
  console.log('保存系统设置:', systemSettings.value)
  // 这里会调用API保存数据
}

const savePointLevels = () => {
  console.log('保存等级设置:', pointLevels.value)
  // 这里会调用API保存数据
}

const addLevel = () => {
  const newId = Math.max(...pointLevels.value.map(l => l.id)) + 1
  pointLevels.value.push({
    id: newId,
    name: '',
    minPoints: 0,
    maxPoints: 0,
    benefits: ''
  })
}

const removeLevel = (index) => {
  pointLevels.value.splice(index, 1)
}
</script>