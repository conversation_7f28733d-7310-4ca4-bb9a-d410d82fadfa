<template>
  <div class="points-data">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">积分数据</h1>
      <p class="text-gray-600">查看积分统计和流水记录</p>
    </div>

    <!-- 积分统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-currency-dollar" class="w-6 h-6 text-blue-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ pointsStats.totalIssued.toLocaleString() }}</p>
          <p class="text-sm text-gray-500">总发放积分</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-arrow-trending-up" class="w-6 h-6 text-green-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ pointsStats.totalUsed.toLocaleString() }}</p>
          <p class="text-sm text-gray-500">总消费积分</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-wallet" class="w-6 h-6 text-purple-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ pointsStats.totalRemaining.toLocaleString() }}</p>
          <p class="text-sm text-gray-500">剩余积分</p>
        </div>
      </UCard>
    </div>

    <!-- 筛选和搜索 -->
    <UCard class="mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索员工姓名或工号..."
            icon="i-heroicons-magnifying-glass"
          />
        </div>
        <div class="flex gap-2">
          <USelect
            v-model="selectedType"
            :options="typeOptions"
            placeholder="积分类型"
          />
          <USelect
            v-model="selectedDepartment"
            :options="departmentOptions"
            placeholder="部门"
          />
          <UButton @click="resetFilters" variant="outline">重置</UButton>
        </div>
      </div>
    </UCard>

    <!-- 积分流水表格 -->
    <UCard>
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">积分流水记录</h3>
          <UButton @click="exportData" variant="outline" size="sm">
            <UIcon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-2" />
            导出数据
          </UButton>
        </div>
      </template>

      <UTable
        :rows="filteredTransactions"
        :columns="columns"
        :loading="loading"
      >
        <template #employee-data="{ row }">
          <div class="flex items-center space-x-3">
            <UAvatar :alt="row.employee.name" size="sm" />
            <div>
              <p class="text-sm font-medium text-gray-900">{{ row.employee.name }}</p>
              <p class="text-xs text-gray-500">{{ row.employee.department }}</p>
            </div>
          </div>
        </template>

        <template #type-data="{ row }">
          <UBadge
            :color="getTypeColor(row.type)"
            variant="soft"
          >
            {{ getTypeLabel(row.type) }}
          </UBadge>
        </template>

        <template #points-data="{ row }">
          <span :class="row.type === 'earn' ? 'text-green-600' : 'text-red-600'" class="font-medium">
            {{ row.type === 'earn' ? '+' : '-' }}{{ row.points }}
          </span>
        </template>

        <template #date-data="{ row }">
          <div>
            <p class="text-sm text-gray-900">{{ formatDate(row.date) }}</p>
            <p class="text-xs text-gray-500">{{ formatTime(row.date) }}</p>
          </div>
        </template>
      </UTable>

      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <p class="text-sm text-gray-500">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalRecords) }}
          条，共 {{ totalRecords }} 条记录
        </p>
        <UPagination
          v-model="currentPage"
          :page-count="pageSize"
          :total="totalRecords"
        />
      </div>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedType = ref('')
const selectedDepartment = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 积分统计数据
const pointsStats = ref({
  totalIssued: 125680,
  totalUsed: 89420,
  totalRemaining: 36260
})

// 筛选选项
const typeOptions = [
  { label: '全部类型', value: '' },
  { label: '获得积分', value: 'earn' },
  { label: '消费积分', value: 'spend' }
]

const departmentOptions = [
  { label: '全部部门', value: '' },
  { label: '销售部', value: 'sales' },
  { label: '技术部', value: 'tech' },
  { label: '市场部', value: 'marketing' },
  { label: '人事部', value: 'hr' },
  { label: '财务部', value: 'finance' }
]

// 表格列定义
const columns = [
  { key: 'employee', label: '员工信息' },
  { key: 'type', label: '类型' },
  { key: 'points', label: '积分变动' },
  { key: 'description', label: '说明' },
  { key: 'date', label: '时间' }
]

// 模拟积分流水数据
const transactions = ref([
  {
    id: 1,
    employee: { name: '张三', department: '销售部', avatar: '' },
    type: 'earn',
    points: 100,
    description: '完成月度销售目标',
    date: new Date('2024-01-15 14:30:00')
  },
  {
    id: 2,
    employee: { name: '李四', department: '技术部', avatar: '' },
    type: 'spend',
    points: 50,
    description: '兑换咖啡券',
    date: new Date('2024-01-15 10:20:00')
  },
  {
    id: 3,
    employee: { name: '王五', department: '市场部', avatar: '' },
    type: 'earn',
    points: 80,
    description: '参与团队建设活动',
    date: new Date('2024-01-14 16:45:00')
  },
  {
    id: 4,
    employee: { name: '赵六', department: '人事部', avatar: '' },
    type: 'earn',
    points: 60,
    description: '协助新员工入职',
    date: new Date('2024-01-14 09:15:00')
  },
  {
    id: 5,
    employee: { name: '钱七', department: '财务部', avatar: '' },
    type: 'spend',
    points: 120,
    description: '兑换购物卡',
    date: new Date('2024-01-13 15:30:00')
  }
])

// 计算属性
const totalRecords = computed(() => filteredTransactions.value.length)

const filteredTransactions = computed(() => {
  let filtered = transactions.value

  if (searchQuery.value) {
    filtered = filtered.filter(t =>
      t.employee.name.includes(searchQuery.value) ||
      t.description.includes(searchQuery.value)
    )
  }

  if (selectedType.value) {
    filtered = filtered.filter(t => t.type === selectedType.value)
  }

  if (selectedDepartment.value) {
    filtered = filtered.filter(t => {
      const deptMap = {
        'sales': '销售部',
        'tech': '技术部',
        'marketing': '市场部',
        'hr': '人事部',
        'finance': '财务部'
      }
      return t.employee.department === deptMap[selectedDepartment.value]
    })
  }

  return filtered
})

// 方法
const getTypeColor = (type) => {
  return type === 'earn' ? 'green' : 'red'
}

const getTypeLabel = (type) => {
  return type === 'earn' ? '获得积分' : '消费积分'
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedType.value = ''
  selectedDepartment.value = ''
  currentPage.value = 1
}

const exportData = () => {
  // 导出数据逻辑
  console.log('导出积分数据')
}
</script>