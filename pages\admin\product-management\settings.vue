<template>
  <div class="page-container">
    <h2>商品设置与管理</h2>
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>商品名称</th>
            <th>所需积分</th>
            <th>库存</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="product in products" :key="product.id">
            <!-- Display Mode -->
            <template v-if="editingProductId !== product.id">
              <td>{{ product.name }}</td>
              <td>{{ product.points }}</td>
              <td>{{ product.stock }}</td>
              <td>
                <span :class="['status', product.status]">{{ product.status === 'active' ? '上架中' : '已下架' }}</span>
              </td>
              <td>
                <button @click="startEditing(product)" class="btn-edit">编辑</button>
              </td>
            </template>
            <!-- Editing Mode -->
            <template v-else>
              <td><input type="text" v-model="editingProductData.name"></td>
              <td><input type="number" v-model.number="editingProductData.points"></td>
              <td><input type="number" v-model.number="editingProductData.stock"></td>
              <td>
                <select v-model="editingProductData.status">
                  <option value="active">上架中</option>
                  <option value="inactive">已下架</option>
                </select>
              </td>
              <td>
                <button @click="saveChanges" class="btn-save">保存</button>
                <button @click="cancelEditing" class="btn-cancel">取消</button>
              </td>
            </template>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { useProducts } from '~/composables/useProducts';
import { ref } from 'vue';

const { products, updateProduct } = useProducts();

const editingProductId = ref(null);
const editingProductData = ref(null);

const startEditing = (product) => {
  editingProductId.value = product.id;
  editingProductData.value = JSON.parse(JSON.stringify(product)); // Deep copy
};

const saveChanges = () => {
  if (editingProductData.value) {
    updateProduct(editingProductData.value);
  }
  cancelEditing();
};

const cancelEditing = () => {
  editingProductId.value = null;
  editingProductData.value = null;
};
</script>

<style scoped>
.page-container {
  max-width: 1200px;
}
h2 { margin-bottom: 1.5rem; }
.table-container { background: #fff; padding: 1rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); overflow-x: auto; }
table { width: 100%; border-collapse: collapse; }
th, td { padding: 1rem; text-align: left; border-bottom: 1px solid #eee; vertical-align: middle; }
th { font-weight: bold; }
td input, td select { width: 100%; padding: 0.5rem; box-sizing: border-box; }
.status { padding: .25rem .5rem; border-radius: 12px; font-size: .8rem; font-weight: bold; }
.status.active { background-color: #d1e7dd; color: #0f5132; }
.status.inactive { background-color: #f8d7da; color: #842029; }
button { padding: .5rem 1rem; border: none; border-radius: 4px; cursor: pointer; margin-right: 0.5rem; }
.btn-edit { background-color: #ffc107; color: #000; }
.btn-save { background-color: #198754; color: #fff; }
.btn-cancel { background-color: #6c757d; color: #fff; }
</style>