<template>
  <div class="product-settings">
    <div class="header mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">商品管理</h1>
      <p class="text-gray-600">管理积分商城商品库存和设置</p>
    </div>

    <!-- 商品统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-gift" class="w-6 h-6 text-blue-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ productStats.total }}</p>
          <p class="text-sm text-gray-500">商品总数</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-check-circle" class="w-6 h-6 text-green-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ productStats.active }}</p>
          <p class="text-sm text-gray-500">上架商品</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-yellow-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ productStats.lowStock }}</p>
          <p class="text-sm text-gray-500">库存不足</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <UIcon name="i-heroicons-arrow-trending-up" class="w-6 h-6 text-purple-600" />
          </div>
          <p class="text-2xl font-bold text-gray-900">{{ productStats.totalExchanges }}</p>
          <p class="text-sm text-gray-500">总兑换次数</p>
        </div>
      </UCard>
    </div>

    <!-- 筛选和搜索 -->
    <UCard class="mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索商品名称或编码..."
            icon="i-heroicons-magnifying-glass"
          />
        </div>
        <div class="flex gap-2">
          <USelect
            v-model="selectedCategory"
            :options="categoryOptions"
            placeholder="分类"
          />
          <USelect
            v-model="selectedStatus"
            :options="statusOptions"
            placeholder="状态"
          />
          <UButton @click="resetFilters" variant="outline">重置</UButton>
          <UButton to="/admin/product-management/add" color="blue">
            <UIcon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
            添加商品
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- 商品列表 -->
    <UCard>
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">商品列表</h3>
          <div class="flex space-x-2">
            <UButton @click="batchOperation" variant="outline" size="sm">
              批量操作
            </UButton>
            <UButton @click="exportProducts" variant="outline" size="sm">
              <UIcon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-2" />
              导出
            </UButton>
          </div>
        </div>
      </template>

      <UTable
        :rows="filteredProducts"
        :columns="columns"
        :loading="loading"
        v-model="selectedRows"
      >
        <template #product-data="{ row }">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-gift" class="w-6 h-6 text-gray-500" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ row.name }}</p>
              <p class="text-xs text-gray-500">{{ row.sku }}</p>
            </div>
          </div>
        </template>

        <template #category-data="{ row }">
          <UBadge
            :color="getCategoryColor(row.category)"
            variant="soft"
          >
            {{ getCategoryLabel(row.category) }}
          </UBadge>
        </template>

        <template #points-data="{ row }">
          <div class="text-center">
            <p class="text-sm font-semibold text-gray-900">{{ row.points }}</p>
            <p class="text-xs text-gray-500">积分</p>
          </div>
        </template>

        <template #stock-data="{ row }">
          <div class="text-center">
            <p class="text-sm font-semibold" :class="getStockColor(row.stock)">{{ row.stock }}</p>
            <p class="text-xs text-gray-500">库存</p>
          </div>
        </template>

        <template #status-data="{ row }">
          <UBadge
            :color="getStatusColor(row.status)"
            variant="soft"
          >
            {{ getStatusLabel(row.status) }}
          </UBadge>
        </template>

        <template #exchangeCount-data="{ row }">
          <div class="text-center">
            <p class="text-sm font-semibold text-gray-900">{{ row.exchangeCount }}</p>
            <p class="text-xs text-gray-500">兑换次数</p>
          </div>
        </template>

        <template #actions-data="{ row }">
          <div class="flex space-x-2">
            <UButton @click="viewProduct(row)" variant="ghost" size="sm">
              <UIcon name="i-heroicons-eye" class="w-4 h-4" />
            </UButton>
            <UButton @click="editProduct(row)" variant="ghost" size="sm">
              <UIcon name="i-heroicons-pencil" class="w-4 h-4" />
            </UButton>
            <UButton @click="adjustStock(row)" variant="ghost" size="sm" color="green">
              <UIcon name="i-heroicons-cube" class="w-4 h-4" />
            </UButton>
            <UDropdown :items="getProductActions(row)">
              <UButton variant="ghost" size="sm">
                <UIcon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
              </UButton>
            </UDropdown>
          </div>
        </template>
      </UTable>

      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <p class="text-sm text-gray-500">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalRecords) }}
          条，共 {{ totalRecords }} 条记录
        </p>
        <UPagination
          v-model="currentPage"
          :page-count="pageSize"
          :total="totalRecords"
        />
      </div>
    </UCard>

    <!-- 库存调整模态框 -->
    <UModal v-model="showStockModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">库存调整</h3>
        </template>

        <div v-if="selectedProduct" class="space-y-4">
          <div>
            <p class="text-sm text-gray-500">商品：{{ selectedProduct.name }}</p>
            <p class="text-sm text-gray-500">当前库存：{{ selectedProduct.stock }}</p>
          </div>

          <UFormGroup label="调整类型">
            <URadioGroup
              v-model="stockAdjustment.type"
              :options="[
                { label: '增加库存', value: 'add' },
                { label: '减少库存', value: 'subtract' },
                { label: '设置库存', value: 'set' }
              ]"
            />
          </UFormGroup>

          <UFormGroup label="数量">
            <UInput
              v-model="stockAdjustment.amount"
              type="number"
              placeholder="输入数量"
            />
          </UFormGroup>

          <UFormGroup label="调整原因">
            <UTextarea
              v-model="stockAdjustment.reason"
              placeholder="请输入调整原因..."
              rows="3"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton @click="showStockModal = false" variant="outline">取消</UButton>
            <UButton @click="confirmStockAdjustment" color="blue">确认调整</UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const selectedRows = ref([])
const showStockModal = ref(false)
const selectedProduct = ref(null)

// 商品统计数据
const productStats = ref({
  total: 60,
  active: 45,
  lowStock: 8,
  totalExchanges: 1256
})

// 库存调整数据
const stockAdjustment = ref({
  type: 'add',
  amount: 0,
  reason: ''
})

// 筛选选项
const categoryOptions = [
  { label: '全部分类', value: '' },
  { label: '生活用品', value: 'life' },
  { label: '办公用品', value: 'office' },
  { label: '电子产品', value: 'electronics' },
  { label: '食品饮料', value: 'food' },
  { label: '服装配饰', value: 'clothing' },
  { label: '图书文具', value: 'books' },
  { label: '健康保健', value: 'health' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '上架', value: 'active' },
  { label: '下架', value: 'inactive' },
  { label: '预售', value: 'presale' }
]

// 表格列定义
const columns = [
  { key: 'product', label: '商品信息' },
  { key: 'category', label: '分类' },
  { key: 'points', label: '积分' },
  { key: 'stock', label: '库存' },
  { key: 'status', label: '状态' },
  { key: 'exchangeCount', label: '兑换次数' },
  { key: 'actions', label: '操作' }
]

// 模拟商品数据
const products = ref([
  {
    id: 1,
    name: '星巴克咖啡券',
    sku: 'PROD001',
    category: 'food',
    points: 50,
    stock: 100,
    status: 'active',
    exchangeCount: 156,
    marketPrice: 35
  },
  {
    id: 2,
    name: '无线蓝牙鼠标',
    sku: 'PROD002',
    category: 'electronics',
    points: 200,
    stock: 25,
    status: 'active',
    exchangeCount: 89,
    marketPrice: 150
  },
  {
    id: 3,
    name: '保温杯',
    sku: 'PROD003',
    category: 'life',
    points: 150,
    stock: 5,
    status: 'active',
    exchangeCount: 67,
    marketPrice: 120
  },
  {
    id: 4,
    name: '笔记本',
    sku: 'PROD004',
    category: 'office',
    points: 30,
    stock: 200,
    status: 'active',
    exchangeCount: 234,
    marketPrice: 25
  },
  {
    id: 5,
    name: '运动手环',
    sku: 'PROD005',
    category: 'electronics',
    points: 500,
    stock: 0,
    status: 'inactive',
    exchangeCount: 45,
    marketPrice: 399
  }
])

// 计算属性
const totalRecords = computed(() => filteredProducts.value.length)

const filteredProducts = computed(() => {
  let filtered = products.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(query) ||
      product.sku.toLowerCase().includes(query)
    )
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(product => product.category === selectedCategory.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(product => product.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const getCategoryColor = (category) => {
  const colorMap = {
    'life': 'blue',
    'office': 'green',
    'electronics': 'purple',
    'food': 'orange',
    'clothing': 'pink',
    'books': 'yellow',
    'health': 'red',
    'other': 'gray'
  }
  return colorMap[category] || 'gray'
}

const getCategoryLabel = (category) => {
  const labelMap = {
    'life': '生活用品',
    'office': '办公用品',
    'electronics': '电子产品',
    'food': '食品饮料',
    'clothing': '服装配饰',
    'books': '图书文具',
    'health': '健康保健',
    'other': '其他'
  }
  return labelMap[category] || category
}

const getStockColor = (stock) => {
  if (stock === 0) return 'text-red-600'
  if (stock < 10) return 'text-yellow-600'
  return 'text-gray-900'
}

const getStatusColor = (status) => {
  const colorMap = {
    'active': 'green',
    'inactive': 'red',
    'presale': 'yellow'
  }
  return colorMap[status] || 'gray'
}

const getStatusLabel = (status) => {
  const labelMap = {
    'active': '上架',
    'inactive': '下架',
    'presale': '预售'
  }
  return labelMap[status] || status
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}

const batchOperation = () => {
  console.log('批量操作:', selectedRows.value)
}

const exportProducts = () => {
  console.log('导出商品数据')
}

const viewProduct = (product) => {
  console.log('查看商品:', product)
}

const editProduct = (product) => {
  console.log('编辑商品:', product)
}

const adjustStock = (product) => {
  selectedProduct.value = product
  stockAdjustment.value = {
    type: 'add',
    amount: 0,
    reason: ''
  }
  showStockModal.value = true
}

const getProductActions = (product) => {
  return [
    [{
      label: product.status === 'active' ? '下架商品' : '上架商品',
      icon: product.status === 'active' ? 'i-heroicons-eye-slash' : 'i-heroicons-eye',
      click: () => toggleProductStatus(product)
    }],
    [{
      label: '复制商品',
      icon: 'i-heroicons-document-duplicate',
      click: () => duplicateProduct(product)
    }],
    [{
      label: '删除商品',
      icon: 'i-heroicons-trash',
      click: () => deleteProduct(product)
    }]
  ]
}

const toggleProductStatus = (product) => {
  const newStatus = product.status === 'active' ? 'inactive' : 'active'
  console.log(`${product.name} 状态变更为: ${newStatus}`)
}

const duplicateProduct = (product) => {
  console.log('复制商品:', product)
}

const deleteProduct = (product) => {
  if (confirm(`确定要删除商品 ${product.name} 吗？`)) {
    const index = products.value.findIndex(p => p.id === product.id)
    if (index > -1) {
      products.value.splice(index, 1)
    }
  }
}

const confirmStockAdjustment = () => {
  console.log('库存调整:', {
    product: selectedProduct.value,
    adjustment: stockAdjustment.value
  })
  // 这里会调用API进行库存调整
  showStockModal.value = false
}
</script>